import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import Layout from "@/components/layout/Layout";
import { mockArticles, editorsPicksArticles, advertisements } from "@/data/mockData";
import { Article } from "@/types";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CalendarIcon, MessageSquare, Share2, Bookmark, ThumbsUp, Facebook, Twitter } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import EditorsPicks from "@/components/news/EditorsPicks";
import AdvertisementPanel from "@/components/news/AdvertisementPanel";

export default function ArticlePage() {
  const { id } = useParams<{ id: string }>();
  const [article, setArticle] = useState<Article | undefined>(undefined);
  const [comment, setComment] = useState("");
  
  useEffect(() => {
    // In a real application, this would be an API call
    const foundArticle = mockArticles.find((article) => article.id === id);
    setArticle(foundArticle);
  }, [id]);
  
  if (!article) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12 text-center">
          <h1 className="text-2xl font-bold mb-4">Article not found</h1>
          <p>The article you are looking for does not exist or has been removed.</p>
        </div>
      </Layout>
    );
  }
  
  const date = new Date(article.publishedAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  
  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real application, this would be an API call to submit the comment
    console.log("Comment submitted:", comment);
    setComment("");
  };
  
  return (
    <Layout>
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-8 space-y-6">
            {/* Article Header */}
            <div>
              <Badge className="mb-4 bg-[#1976D2] hover:bg-blue-700">{article.category}</Badge>
              <h1 className="text-3xl md:text-4xl font-bold mb-4">{article.title}</h1>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarImage src="https://source.unsplash.com/random/100x100?person" alt="Author" />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold">{article.author}</p>
                    <div className="flex items-center text-sm text-gray-500">
                      <CalendarIcon size={14} className="mr-1" />
                      <span>{date}</span>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button size="icon" variant="outline">
                    <Share2 size={18} />
                  </Button>
                  <Button size="icon" variant="outline">
                    <Bookmark size={18} />
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Article Image */}
            <div className="rounded-lg overflow-hidden">
              <img 
                src={article.imageUrl} 
                alt={article.title} 
                className="w-full object-cover h-[300px] md:h-[400px]" 
              />
            </div>
            
            {/* Article Content */}
            <div className="prose max-w-none">
              <div dangerouslySetInnerHTML={{ __html: article.content }} />
            </div>
            
            {/* Social Share */}
            <div className="border-t border-b py-4 flex justify-between items-center">
              <div className="flex items-center">
                <Button variant="ghost" className="text-[#D32F2F]">
                  <ThumbsUp size={18} className="mr-2" /> Like
                </Button>
                <Button variant="ghost">
                  <MessageSquare size={18} className="mr-2" /> Comment
                </Button>
              </div>
              <div className="flex space-x-2">
                <Button size="icon" variant="outline" className="text-[#1976D2]">
                  <Facebook size={18} />
                </Button>
                <Button size="icon" variant="outline" className="text-[#1976D2]">
                  <Twitter size={18} />
                </Button>
              </div>
            </div>
            
            {/* Comments */}
            <div>
              <h3 className="text-xl font-bold mb-4">Comments</h3>
              <form onSubmit={handleCommentSubmit} className="mb-6">
                <Textarea 
                  placeholder="Write your comment..." 
                  className="mb-2"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                />
                <Button className="bg-[#D32F2F] hover:bg-red-700">Submit Comment</Button>
              </form>
              
              {article.comments.length > 0 ? (
                <div className="space-y-4">
                  {article.comments.map((comment) => (
                    <div key={comment.id} className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>{comment.username[0]}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-semibold text-sm">{comment.username}</p>
                          <p className="text-xs text-gray-500">{
                            new Date(comment.createdAt).toLocaleDateString()
                          }</p>
                        </div>
                      </div>
                      <p className="text-sm">{comment.content}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No comments yet. Be the first to comment!</p>
              )}
            </div>
          </div>
          
          {/* Sidebar */}
          <div className="lg:col-span-4 space-y-6">
            <AdvertisementPanel advertisements={advertisements} />
            <EditorsPicks articles={editorsPicksArticles} />
          </div>
        </div>
      </div>
    </Layout>
  );
}