import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useState } from "react";
import { Search, Menu, X } from "lucide-react";
import { Link } from "react-router-dom";

const LoginDialog = () => {
  const [activeTab, setActiveTab] = useState<string>("login");

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">Login / Register</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Account</DialogTitle>
        </DialogHeader>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="login">Login</TabsTrigger>
            <TabsTrigger value="register">Register</TabsTrigger>
          </TabsList>
          <div className="mt-4">
            {activeTab === "login" ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium">
                    Email
                  </label>
                  <Input id="email" placeholder="Enter your email" />
                </div>
                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-medium">
                    Password
                  </label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                  />
                </div>
                <Button className="w-full bg-[#D32F2F] hover:bg-red-700">
                  Login
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-medium">
                    Full Name
                  </label>
                  <Input id="name" placeholder="Enter your name" />
                </div>
                <div className="space-y-2">
                  <label htmlFor="reg-email" className="text-sm font-medium">
                    Email
                  </label>
                  <Input id="reg-email" placeholder="Enter your email" />
                </div>
                <div className="space-y-2">
                  <label htmlFor="reg-password" className="text-sm font-medium">
                    Password
                  </label>
                  <Input
                    id="reg-password"
                    type="password"
                    placeholder="Create a password"
                  />
                </div>
                <Button className="w-full bg-[#D32F2F] hover:bg-red-700">
                  Register
                </Button>
              </div>
            )}
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <header className="border-b bg-white">
      <div className="container mx-auto px-4">
        {/* Top bar */}
        <div className="flex items-center justify-between py-4">
          <Link to="/" className="flex items-center space-x-2">
            <h1 className="text-2xl font-bold text-[#D32F2F]">THE SACH PATRA</h1>
          </Link>
          <div className="hidden md:flex items-center space-x-4">
            <div className="relative">
              <Input
                type="search"
                placeholder="Search news..."
                className="w-64 pr-10"
              />
              <Search className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
            </div>
            <LoginDialog />
          </div>
          <button
            className="md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? (
              <X className="h-6 w-6 text-[#D32F2F]" />
            ) : (
              <Menu className="h-6 w-6 text-[#D32F2F]" />
            )}
          </button>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="py-4 md:hidden border-t">
            <div className="space-y-4">
              <div className="relative">
                <Input
                  type="search"
                  placeholder="Search news..."
                  className="w-full pr-10"
                />
                <Search className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>
              <div className="flex justify-between items-center">
                <LoginDialog />
              </div>
              <nav className="space-y-2">
                <Link to="/category/politics" className="block py-2 hover:text-[#D32F2F]">Politics</Link>
                <Link to="/category/tech" className="block py-2 hover:text-[#D32F2F]">Tech</Link>
                <Link to="/category/world" className="block py-2 hover:text-[#D32F2F]">World</Link>
                <Link to="/category/sports" className="block py-2 hover:text-[#D32F2F]">Sports</Link>
                <Link to="/category/entertainment" className="block py-2 hover:text-[#D32F2F]">Entertainment</Link>
              </nav>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav className="hidden md:flex items-center justify-between py-2">
          <div className="flex space-x-6 text-sm font-medium">
            <Link
              to="/category/politics"
              className="px-2 py-1 hover:text-[#D32F2F] transition-colors"
            >
              Politics
            </Link>
            <Link
              to="/category/tech"
              className="px-2 py-1 hover:text-[#D32F2F] transition-colors"
            >
              Tech
            </Link>
            <Link
              to="/category/world"
              className="px-2 py-1 hover:text-[#D32F2F] transition-colors"
            >
              World
            </Link>
            <Link
              to="/category/sports"
              className="px-2 py-1 hover:text-[#D32F2F] transition-colors"
            >
              Sports
            </Link>
            <Link
              to="/category/entertainment"
              className="px-2 py-1 hover:text-[#D32F2F] transition-colors"
            >
              Entertainment
            </Link>
          </div>
          <div>
            <span className="text-xs text-muted-foreground">
              {new Date().toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </span>
          </div>
        </nav>
      </div>
    </header>
  );
}