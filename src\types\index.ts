export interface Article {
  id: string;
  title: string;
  summary: string;
  content: string;
  category: Category;
  imageUrl: string;
  author: string;
  publishedAt: string;
  isFeatured?: boolean;
  isBreaking?: boolean;
  isTrending?: boolean;
  views: number;
  comments: Comment[];
}

export type Category = 'Politics' | 'Tech' | 'World' | 'Sports' | 'Entertainment';

export interface Comment {
  id: string;
  articleId: string;
  userId: string;
  username: string;
  content: string;
  createdAt: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'user' | 'admin';
}